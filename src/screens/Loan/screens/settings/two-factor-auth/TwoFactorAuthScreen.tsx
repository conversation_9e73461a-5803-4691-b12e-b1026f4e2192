import LoadingHandler from '@/components/LoadingHandler';
import MButton from '@/components/MButton';
import {CONSTANTS} from '@/screens/LightningNetwork/helpers/lightningConstants';
import {Footer} from '@/styles/styled-components';
import theme from '@/styles/theme';
import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import React, {useEffect, useState} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {
  KeyboardAwareScrollView,
  KeyboardStickyView,
} from 'react-native-keyboard-controller';
import {VerificationCodeInput} from './VerificationCodeInput';
import {useLoan} from '@/hooks/redux';
import {useMutation, useQuery} from '@tanstack/react-query';

const TwoFactorAuthScreen: React.FC = () => {
  const {accessToken} = useLoan();

  console.log('ACCESS TOKEN >>>', accessToken);

  const [qrCodeData, setQrCodeData] = useState<any>();
  const [code, setCode] = useState('');

  const height = useBottomTabBarHeight();

  const onSubmit = () => {};

  const fetchQrCode = async () => {
    const response = await fetch('http://192.168.10.157/auth/user/otp/generate', {
      method: 'POST',
      headers: {
        accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
    });

    return response.json();
  };

  const {data, isLoading, isError} = useMutation({
    queryKey: ['qrCode'],
    queryFn: fetchQrCode,
    enabled: !!accessToken,
  });

  console.log('DATA >>>', data);

  console.log('IS LOADING >>>', isLoading);

  console.log('IS ERROR >>>', isError);

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        keyboardShouldPersistTaps="handled"
        bottomOffset={140}
      >
        <Text style={styles.descText}>Set up two-factor authentication.</Text>
        <Text style={styles.descSubText}>
          To be able to authorize transactions you need to scan this QR Code with your
          Authentication App and enter the verification code below.
        </Text>

        <View>
          {!isLoading ? (
            <View style={styles.qr}>
              <LoadingHandler />
            </View>
          ) : (
            <TouchableOpacity>
              <Image style={styles.qr} source={{uri: data?.data}} />
            </TouchableOpacity>
          )}
        </View>

        <Text style={styles.verificationLabel}>Verification Code</Text>
        <VerificationCodeInput value={code} onChange={setCode} length={6} />
      </KeyboardAwareScrollView>

      <KeyboardStickyView
        offset={{closed: 0, opened: height + (theme.isSmallDevice ? 24 : 40)}}
      >
        <Footer>
          <MButton text="Next" disabled={code.length !== 6} onPress={onSubmit} />
        </Footer>
      </KeyboardStickyView>
    </View>
  );
};

export default TwoFactorAuthScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 32,
    paddingTop: 42,
  },
  contentContainer: {
    flexGrow: 1,
  },
  descText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  descSubText: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 24,
  },
  verificationLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    marginTop: 16,
  },
  qr: {
    width: 200,
    height: 200,
    resizeMode: 'contain',
    alignSelf: 'center',
  },
  stickyFooter: {
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
});
